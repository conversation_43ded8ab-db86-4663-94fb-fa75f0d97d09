'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import DataTable from '@/components/common/DataTable';
import DocumentPreviewModal from '@/components/documents/DocumentPreviewModal';
import { useAuth } from '@/contexts/AuthContext';
import { documentService } from '@/services/documentService';
import { PaginateQuery, PaginatedResponse } from '@/services/userService';

// Document interface based on the backend entity
interface Document {
  document_id: string;
  document_type: string;
  file_name: string;
  entity_type: string;
  entity_id: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  is_required: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string;
  creator?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

const CustomerDocumentsPage = () => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();
  const [documentsData, setDocumentsData] = useState<PaginatedResponse<Document> | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState<boolean>(false);

  // Use imported document service

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Load documents function
  const loadDocuments = useCallback(async (query: PaginateQuery) => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      console.log('Loading documents with query:', query);

      // Use the document service to get documents (backend will filter by user)
      const response = await documentService.getDocuments({
        page: query.page,
        limit: query.limit,
        search: query.search,
        sortBy: query.sortBy?.join(','),
      });

      console.log('Documents loaded successfully:', response);
      setDocumentsData(response);
    } catch (err: any) {
      console.error('Error loading documents:', err);
      setError(err.message || 'Failed to load documents');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // Load documents on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadDocuments({
        page: 1,
        limit: 10,
        search: '',
        sortBy: ['created_at:DESC'],
      });
    }
  }, [isAuthenticated, loadDocuments]);

  // Format file size helper
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date helper
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get document type badge
  const getDocumentTypeBadge = (type: string): React.ReactElement => {
    const typeConfig: Record<string, { color: string; label: string }> = {
      'certificate_incorporation': { color: 'bg-blue-100 text-blue-800', label: 'Certificate' },
      'memorandum_association': { color: 'bg-green-100 text-green-800', label: 'Memorandum' },
      'shareholding_structure': { color: 'bg-purple-100 text-purple-800', label: 'Shareholding' },
      'business_plan': { color: 'bg-orange-100 text-orange-800', label: 'Business Plan' },
      'financial_statements': { color: 'bg-red-100 text-red-800', label: 'Financial' },
      'cv': { color: 'bg-yellow-100 text-yellow-800', label: 'CV' },
      'other': { color: 'bg-gray-100 text-gray-800', label: 'Other' },
    };

    const config = typeConfig[type.toLowerCase()] || typeConfig['other'];

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  // Download document handler
  const handleDownload = async (doc: Document) => {
    try {
      console.log('Downloading document:', doc.document_id);
      const blob = await documentService.downloadDocument(doc.document_id);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = doc.file_name;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      console.error('Error downloading document:', err);
      setError('Failed to download document');
    }
  };
  // Define table columns
  const documentColumns = [
    {
      key: 'file_name',
      label: 'Document Name',
      sortable: true,
      render: (value: string, item: Document) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
              <i className="ri-file-text-line text-gray-500 dark:text-gray-400"></i>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {value}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {item.mime_type}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'document_type',
      label: 'Type',
      sortable: true,
      render: (value: string) => getDocumentTypeBadge(value),
    },
    {
      key: 'entity_type',
      label: 'Related To',
      sortable: true,
      render: (value: string, item: Document) => (
        <div>
          <div className="text-sm text-gray-900 dark:text-gray-100 capitalize">
            {value}
          </div>
          {item.entity_id && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              ID: {item.entity_id.substring(0, 8)}...
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'file_size',
      label: 'Size',
      sortable: true,
      render: (value: number) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {formatFileSize(value)}
        </span>
      ),
    },
    {
      key: 'is_required',
      label: 'Required',
      sortable: true,
      render: (value: boolean) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          value
            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
        }`}>
          {value ? 'Required' : 'Optional'}
        </span>
      ),
    },
    {
      key: 'created_at',
      label: 'Uploaded',
      sortable: true,
      render: (value: string) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {formatDate(value)}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: any, item: Document) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleDownload(item)}
            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
            title="Download"
          >
            <i className="ri-download-line"></i>
          </button>
          <button
            onClick={() => handleViewDocument(item)}
            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
            title="View Details"
          >
            <i className="ri-eye-line"></i>
          </button>
        </div>
      ),
    },
  ];

  // View document details handler
  const handleViewDocument = (doc: Document) => {
    setSelectedDocument(doc);
    setIsPreviewModalOpen(true);
  };

  // Close preview modal
  const handleClosePreview = () => {
    setIsPreviewModalOpen(false);
    setSelectedDocument(null);
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Redirect if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  return (
    <CustomerLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  My Documents
                </h1>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  View and manage all your uploaded documents
                </p>
              </div>
              <div className="flex items-center space-x-3">
                {documentsData?.meta && (
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {documentsData.meta.totalItems} document{documentsData.meta.totalItems !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <i className="ri-error-warning-line text-red-400"></i>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Error loading documents
                </h3>
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  {error}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Documents Table */}
        <DataTable
          columns={documentColumns}
          data={documentsData}
          loading={loading}
          onQueryChange={loadDocuments}
          searchPlaceholder="Search documents by name, type, or entity..."
        />

        {/* Document Preview Modal */}
        <DocumentPreviewModal
          document={selectedDocument}
          isOpen={isPreviewModalOpen}
          onClose={handleClosePreview}
          onDownload={handleDownload}
        />
      </div>
    </CustomerLayout>
  );
};

export default CustomerDocumentsPage;