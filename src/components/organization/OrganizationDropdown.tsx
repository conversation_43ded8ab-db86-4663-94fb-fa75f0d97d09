import { useState, useRef, useEffect } from 'react';
import { Organization } from '@/types/organization';

interface OrganizationDropdownProps {
  organizations: Organization[];
  selectedOrganizationId: string | null;
  onSelect: (id: string) => void;
}

function OrganizationDropdown({ organizations, selectedOrganizationId, onSelect }: OrganizationDropdownProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (!open) return;

      if (event.key === 'Escape') {
        setOpen(false);
      }
    }

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [open]);

  const selected = organizations.find((o) => o.organization_id === selectedOrganizationId);

  // Filter organizations based on search term only
  const filteredOrganizations = organizations.filter(org => {
    return searchTerm === '' ||
      org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      org.registration_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (org.tpin && org.tpin.toLowerCase().includes(searchTerm.toLowerCase()));
  });

  // Add "Other" option to the filtered list
  const organizationsWithOther = [
    ...filteredOrganizations,
    {
      organization_id: 'other',
      name: 'Other',
      registration_number: '',
      tpin: '',
      website: '',
      email: '',
      phone: '',
      postal_address: '',
      physical_address: '',
      date_incorporation: '',
      place_incorporation: '',
      profile_description: '',
      created_at: '',
      updated_at: ''
    } as Organization
  ];

  return (
    <div className="w-full" ref={dropdownRef}>
      <label className="block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm">
        Organization
      </label>

      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setOpen(true);
          }}
          onFocus={() => setOpen(true)}
          placeholder="Search organizations..."
          className={`w-full px-3 py-2 pr-10 border rounded-md shadow-sm
            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
            focus:outline-none focus:ring-2 focus:ring-primary
            ${open ? 'border-red-500 ring-2 ring-red-500' : 'border-gray-300 dark:border-gray-600'}
          `}
          aria-haspopup="listbox"
          aria-expanded={open}
        />
        <button
          type="button"
          onClick={() => setOpen(!open)}
          className="absolute inset-y-0 right-0 flex items-center pr-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <svg
            className={`h-5 w-5 transition-transform duration-200 ${open ? 'rotate-180' : ''}`}
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z"
              clipRule="evenodd"
            />
          </svg>
        </button>

        {/* Selected Organization Display */}
        {selected && (
          <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
            <div className="flex items-center justify-between">
              <div>
                <span className="text-sm font-medium text-green-800 dark:text-green-200">
                  Selected: {selected.name}
                </span>
                {selected.organization_id !== 'other' && (
                  <div className="text-xs text-green-600 dark:text-green-400">
                    {selected.registration_number} {selected.tpin && `• ${selected.tpin}`}
                  </div>
                )}
              </div>
              <button
                type="button"
                onClick={() => onSelect('')}
                className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200"
                title="Clear selection"
              >
                <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        )}

        {open && (
          <ul
            className="absolute z-50 bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto"
            role="listbox"
          >
            {organizationsWithOther.length > 1 ? (
              organizationsWithOther.map((org) => (
                <li
                  key={org.organization_id}
                  onClick={() => {
                    onSelect(org.organization_id);
                    setOpen(false);
                  }}
                  className={`cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700 ${
                    selectedOrganizationId === org.organization_id
                      ? 'font-semibold text-red-600 dark:text-red-300'
                      : 'text-gray-900 dark:text-gray-100'
                  } ${org.organization_id === 'other' ? 'border-t border-gray-200 dark:border-gray-600 mt-1 pt-3' : ''}`}
                  role="option"
                  aria-selected={selectedOrganizationId === org.organization_id}
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{org.name}</span>
                    {org.organization_id !== 'other' && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {org.registration_number} {org.tpin && `• ${org.tpin}`}
                      </span>
                    )}
                  </div>
                  {selectedOrganizationId === org.organization_id && (
                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300">
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </span>
                  )}
                </li>
              ))
            ) : (
              <li className="text-gray-500 dark:text-gray-400 px-4 py-2">
                No organizations found
                <div className="mt-1">
                  <button
                    onClick={() => {
                      onSelect('other');
                      setOpen(false);
                    }}
                    className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 underline"
                  >
                    Select "Other"
                  </button>
                </div>
              </li>
            )}
          </ul>
        )}
      </div>
    </div>
  );
}

export default OrganizationDropdown;
