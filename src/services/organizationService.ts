import { apiClient } from '../lib/apiClient';
import { processApiResponse } from '../lib/authUtils';

// Types following backend entity structure
export interface Organization {
  organization_id: string;
  name: string;
  registration_number: string;
  website: string;
  email: string;
  phone: string;
  fax?: string;
  physical_address_id?: string;
  postal_address_id?: string;
  contact_id?: string;
  date_incorporation: Date;
  place_incorporation: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  created_by?: string;
  updated_by?: string;
  
  // Related data
  creator?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  
  updater?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };

  // Address relations
  physical_address?: {
    address_id: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };

  postal_address?: {
    address_id: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };

  // Contact relation
  contact?: {
    contact_id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
  };
}

export interface CreateOrganizationDto {
  name: string;
  registration_number: string;
  website: string;
  email: string;
  phone: string;
  fax?: string;
  physical_address_id?: string;
  postal_address_id?: string;
  contact_id?: string;
  date_incorporation: Date;
  place_incorporation: string;
  created_by?: string;
}

export interface UpdateOrganizationDto {
  name?: string;
  registration_number?: string;
  website?: string;
  email?: string;
  phone?: string;
  fax?: string;
  physical_address_id?: string;
  postal_address_id?: string;
  contact_id?: string;
  date_incorporation?: Date;
  place_incorporation?: string;
  updated_by?: string;
}

// Pagination interfaces following department service pattern
export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems: number;
    currentPage: number;
    totalPages: number;
    sortBy: string[][];
    searchBy: string[];
    search: string;
    filter: Record<string, string | string[]>;
  };
  links: {
    first: string;
    previous: string;
    current: string;
    next: string;
    last: string;
  };
}

export interface PaginateQuery {
  page?: number;
  limit?: number;
  sortBy?: string[];
  searchBy?: string[];
  search?: string;
  filter?: Record<string, string | string[]>;
}

export type OrganizationsResponse = PaginatedResponse<Organization>;

export const organizationService = {
  // Create new organization
  async createOrganization(data: CreateOrganizationDto): Promise<Organization> {
    try {
      console.log('🔄 Creating organization:', {
        name: data.name,
        registration_number: data.registration_number,
        email: data.email
      });

      const response = await apiClient.post('/organization', data);
      return processApiResponse(response);
    } catch (error) {
      console.error('❌ Error creating organization:', error);
      throw error;
    }
  },

  // Get organizations with pagination (for management table)
  async getOrganizations(query: PaginateQuery = {}): Promise<OrganizationsResponse> {
    try {
      const params = new URLSearchParams();

      if (query.page) params.set('page', query.page.toString());
      if (query.limit) params.set('limit', query.limit.toString());
      if (query.search) params.set('search', query.search);
      if (query.sortBy) {
        query.sortBy.forEach(sort => params.append('sortBy', sort));
      }
      if (query.searchBy) {
        query.searchBy.forEach(search => params.append('searchBy', search));
      }
      if (query.filter) {
        Object.entries(query.filter).forEach(([key, value]) => {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(`filter.${key}`, v));
          } else {
            params.set(`filter.${key}`, value);
          }
        });
      }

      const response = await apiClient.get(`/organization?${params.toString()}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('Error fetching organizations:', error);
      throw error;
    }
  },

  // Get all organizations (simple list for dropdowns)
  async getAllOrganizations(): Promise<Organization[]> {
    try {
      const response = (await this.getOrganizations()).data;
      return response;
    } catch (error) {
      console.error('Error fetching organizations:', error);
      throw error;
    }
  },

  // Get organization by ID
  async getOrganization(id: string): Promise<Organization> {
    const response = await apiClient.get(`/organization/${id}`);
    return processApiResponse(response);
  },

  // Get organization by ID (alias for consistency)
  async getOrganizationById(id: string): Promise<Organization> {
    return this.getOrganization(id);
  },

  // Update organization
  async updateOrganization(id: string, data: UpdateOrganizationDto): Promise<Organization> {
    const response = await apiClient.put(`/organization/${id}`, data);
    return processApiResponse(response);
  },

  // Delete organization (soft delete)
  async deleteOrganization(id: string): Promise<void> {
    await apiClient.delete(`/organization/${id}`);
  },

  // Helper methods
  formatOrganizationName(name: string): string {
    return name.charAt(0).toUpperCase() + name.slice(1);
  },

  formatRegistrationNumber(regNumber: string): string {
    return regNumber.toUpperCase().trim();
  },

  validateRegistrationNumber(regNumber: string): boolean {
    return /^[A-Z0-9\-]+$/.test(regNumber) && regNumber.length >= 3;
  },

  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  validatePhone(phone: string): boolean {
    return /^[\d\s\-\+\(\)]{5,20}$/.test(phone);
  },

  validateWebsite(website: string): boolean {
    try {
      new URL(website);
      return true;
    } catch {
      return false;
    }
  },

  formatWebsite(website: string): string {
    if (!website.startsWith('http://') && !website.startsWith('https://')) {
      return `https://${website}`;
    }
    return website;
  },

  formatIncorporationDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString();
  },

  validateIncorporationDate(date: Date | string): boolean {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    return dateObj <= now && dateObj.getFullYear() > 1800;
  },
};
